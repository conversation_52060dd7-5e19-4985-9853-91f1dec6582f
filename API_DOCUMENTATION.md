# Family Therapy PIE Notes API Documentation

## Overview

Public REST API for generating HIPAA-compliant PIE (Purpose, Intervention, Evaluation) notes for family therapy sessions using AI.

## Base URL

```
https://your-api-id.execute-api.region.amazonaws.com/prod
```

## Authentication

No authentication required for this public API.

## Endpoints

### POST /generate-pie-notes

Generate PIE notes from therapy session transcript.

#### Request

**URL:** `POST /generate-pie-notes`

**Headers:**
```
Content-Type: application/json
```

**Body Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `transcript` | string | Yes | The therapy session transcript with role labels (therapist, client1, client2, etc.) |
| `target_client_label` | string | Yes | The label of the primary client to focus on (e.g., "client1") |
| `diagnosis_info` | string | No | Optional diagnosis information to include in the PIE note |

**Example Request:**
```json
{
  "transcript": "therapist: Good morning, how are you both feeling today?\nclient1: I've been feeling really anxious about our communication issues.\nclient2: I agree, we seem to argue about everything lately.\ntherapist: Let's explore what's happening in your communication patterns.",
  "target_client_label": "client1",
  "diagnosis_info": "Generalized Anxiety Disorder"
}
```

#### Response

**Success Response (200 OK):**
```json
{
  "statusCode": 200,
  "body": {
    "timestamp": "2025-06-10 13:51:53",
    "target_client": "client1",
    "pie_notes": "**P (Purpose and Problem):** The purpose of the session was to address...\n\n**I (Intervention):** During the session, the therapist employed...\n\n**E (Evaluation):** The client appeared alert and oriented...",
    "processing_info": {
      "total_transcript_tokens": 97,
      "chunks_processed": 1,
      "processing_time_seconds": 17.78
    }
  }
}
```

**Error Response (400 Bad Request):**
```json
{
  "statusCode": 400,
  "body": {
    "error": "transcript is required",
    "fallback_notes": "Unable to generate notes: Missing transcript"
  }
}
```

**Error Response (500 Internal Server Error):**
```json
{
  "statusCode": 200,
  "body": {
    "timestamp": "2025-06-10 13:51:53",
    "target_client": "client1",
    "pie_notes": "P (Purpose and Problem): Session focused on addressing concerns...",
    "error": "Note: Using fallback response due to error: API temporarily unavailable",
    "processing_info": {
      "processing_time_seconds": 0.5
    }
  }
}
```

## Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `statusCode` | number | HTTP status code (200 for success, 400 for client error) |
| `body.timestamp` | string | Timestamp when the note was generated |
| `body.target_client` | string | The target client label from the request |
| `body.pie_notes` | string | The generated PIE note in markdown format |
| `body.processing_info.total_transcript_tokens` | number | Estimated number of tokens in the transcript |
| `body.processing_info.chunks_processed` | number | Number of chunks the transcript was split into |
| `body.processing_info.processing_time_seconds` | number | Time taken to process the request |
| `body.error` | string | Error message (only present if there was an issue) |

## Usage Examples

### cURL
```bash
curl -X POST "https://your-api-id.execute-api.region.amazonaws.com/prod/generate-pie-notes" \
  -H "Content-Type: application/json" \
  -d '{
    "transcript": "therapist: Good morning, how are you both feeling today?\nclient1: I have been feeling anxious lately.",
    "target_client_label": "client1",
    "diagnosis_info": "Generalized Anxiety Disorder"
  }'
```

### JavaScript (Fetch)
```javascript
const response = await fetch('https://your-api-id.execute-api.region.amazonaws.com/prod/generate-pie-notes', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    transcript: "therapist: Good morning, how are you both feeling today?\nclient1: I have been feeling anxious lately.",
    target_client_label: "client1",
    diagnosis_info: "Generalized Anxiety Disorder"
  })
});

const result = await response.json();
console.log(result);
```

### Python (Requests)
```python
import requests

url = "https://your-api-id.execute-api.region.amazonaws.com/prod/generate-pie-notes"
payload = {
    "transcript": "therapist: Good morning, how are you both feeling today?\nclient1: I have been feeling anxious lately.",
    "target_client_label": "client1",
    "diagnosis_info": "Generalized Anxiety Disorder"
}

response = requests.post(url, json=payload)
result = response.json()
print(result)
```

## Rate Limits

- **OpenAI API**: 3 requests per minute (due to OpenAI rate limits)
- **AWS Lambda**: 1000 concurrent executions (default)
- **API Gateway**: 10,000 requests per second (default)

## Error Handling

The API implements graceful error handling:

1. **Validation Errors**: Returns 400 with specific error message
2. **OpenAI API Errors**: Returns 200 with fallback PIE note and error details
3. **Timeout Errors**: Returns 200 with fallback response
4. **Server Errors**: Returns 200 with basic fallback note

## Best Practices

1. **Transcript Format**: Use clear role labels (therapist, client1, client2, etc.)
2. **Length**: Long transcripts are automatically chunked and processed
3. **Timeout**: Allow up to 3 minutes for processing long transcripts
4. **Error Handling**: Always check for the `error` field in responses
5. **Rate Limiting**: Implement client-side rate limiting for high-volume usage

## HIPAA Compliance

- No personal identifiers are stored or logged
- All processing is done in memory
- Transcripts should use role labels instead of real names
- Generated notes are HIPAA-compliant by design

## Support

For issues or questions:
1. Check CloudWatch logs for detailed error information
2. Verify your request format matches the examples
3. Ensure transcript uses proper role labels
