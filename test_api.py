#!/usr/bin/env python3
"""
Test script for the public API Gateway endpoint
"""
import requests
import json
import time

# Replace with your actual API Gateway URL
API_URL = "https://your-api-id.execute-api.region.amazonaws.com/prod/generate-pie-notes"

def test_api_endpoint():
    """Test the API Gateway endpoint"""
    
    print("🚀 Testing API Gateway endpoint...")
    print(f"URL: {API_URL}")
    print("="*60)
    
    # Test payload
    payload = {
        "transcript": """therapist: Good morning, how are you both feeling today?
client1: I've been feeling really anxious about our communication issues.
client2: I agree, we seem to argue about everything lately.
therapist: Let's explore what's happening in your communication patterns.
client1: I feel like client2 never listens to me.
client2: That's not true! I do listen, but client1 always interrupts me.
therapist: I can see both of you are feeling unheard. Let's practice some active listening techniques.""",
        "target_client_label": "client1",
        "diagnosis_info": "Generalized Anxiety Disorder"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("📤 Sending request...")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        start_time = time.time()
        response = requests.post(API_URL, headers=headers, json=payload, timeout=180)
        end_time = time.time()
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Time: {end_time - start_time:.2f} seconds")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"\n✅ API call successful!")
            print(f"📝 Response: {json.dumps(response_data, indent=2)}")
            return True
        else:
            print(f"\n❌ API call failed with status {response.status_code}")
            print(f"📝 Error response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out (>180 seconds)")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - check the API URL")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_api_validation():
    """Test API validation with missing parameters"""
    
    print("\n🔍 Testing API validation...")
    print("="*60)
    
    # Test missing transcript
    test_cases = [
        {
            "name": "Missing transcript",
            "payload": {
                "target_client_label": "client1"
            }
        },
        {
            "name": "Missing target_client_label", 
            "payload": {
                "transcript": "Sample transcript"
            }
        },
        {
            "name": "Empty payload",
            "payload": {}
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Test: {test_case['name']}")
        
        try:
            response = requests.post(API_URL, 
                                   headers={"Content-Type": "application/json"}, 
                                   json=test_case['payload'], 
                                   timeout=30)
            
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text}")
            
        except Exception as e:
            print(f"Error: {str(e)}")

def generate_curl_examples():
    """Generate curl command examples"""
    
    print("\n📋 cURL Examples:")
    print("="*60)
    
    # Basic curl command
    curl_command = f'''curl -X POST "{API_URL}" \\
  -H "Content-Type: application/json" \\
  -d '{{
    "transcript": "therapist: Good morning, how are you both feeling today?\\nclient1: I have been feeling anxious lately.",
    "target_client_label": "client1",
    "diagnosis_info": "Generalized Anxiety Disorder"
  }}'
'''
    
    print("🔧 Basic cURL command:")
    print(curl_command)
    
    # JavaScript fetch example
    js_example = f'''// JavaScript fetch example
const response = await fetch('{API_URL}', {{
  method: 'POST',
  headers: {{
    'Content-Type': 'application/json'
  }},
  body: JSON.stringify({{
    transcript: "therapist: Good morning...",
    target_client_label: "client1", 
    diagnosis_info: "Generalized Anxiety Disorder"
  }})
}});

const result = await response.json();
console.log(result);'''
    
    print("\n🌐 JavaScript example:")
    print(js_example)

if __name__ == "__main__":
    print("🧪 API Gateway Testing Suite")
    print("="*60)
    
    if "your-api-id" in API_URL:
        print("⚠️  Please update API_URL with your actual API Gateway URL!")
        print("You can find it in AWS Console → API Gateway → Your API → Stages → prod")
        print("\nExample URL format:")
        print("https://abc123def4.execute-api.us-east-1.amazonaws.com/prod/generate-pie-notes")
    else:
        # Run tests
        success = test_api_endpoint()
        
        if success:
            test_api_validation()
        
        generate_curl_examples()
        
        print("\n" + "="*60)
        if success:
            print("🎉 API testing completed successfully!")
        else:
            print("❌ API testing failed. Check the URL and try again.")
